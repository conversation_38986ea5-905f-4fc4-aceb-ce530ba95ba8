import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { HawbListObject } from '../../models/hawb-list-object.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { PageEvent } from '@angular/material/paginator';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { HawbSearchComponent } from '../../components/hawb-search/hawb-search.component';
import { HawbTableComponent } from '../../components/hawb-table/hawb-table.component';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { HawbSearchPayload } from '../../models/hawb-search-payload.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Sort } from '@angular/material/sort';
import { ShareType } from '@shared/models/share-type.model';
import { MatDialog } from '@angular/material/dialog';
import { ShareDialogComponent } from '@shared/components/share-dialog/share-dialog.component';

@Component({
	selector: 'orll-hawb-list-page',
	templateUrl: './hawb-list-page.component.html',
	styleUrl: './hawb-list-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [HawbSearchComponent, HawbTableComponent, MatIconModule, TranslateModule, SpinnerComponent],
})
export default class HawbListPageComponent extends DestroyRefComponent implements OnInit {
	@Input() fromCreateMawb = false;
	@Input() isFHL = false;
	@Input() hawbNumberList: string[] = [];

	hawbSearchPayload!: HawbSearchPayload;
	hawbList: HawbListObject[] = [];
	hawbListTotalRecords = 0;
	dataLoading = false;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly cdr: ChangeDetectorRef,
		private readonly dialog: MatDialog,
		private readonly translate: TranslateService
	) {
		super();
	}

	ngOnInit(): void {
		if (this.isFHL && this.hawbNumberList.length) {
			this.hawbSearchPayload = {
				hawbNumberList: this.hawbNumberList,
			};
		}
		this.getHawbListPage(this.pageParams);
	}

	onSearch(hawbSearchPayload: HawbSearchPayload): void {
		this.hawbSearchPayload = hawbSearchPayload;
		this.getHawbListPage(this.pageParams);
	}

	onHawbListSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
		this.getHawbListPage(this.pageParams);
	}

	onHawbListObjectShare(event: HawbListObject): void {
		this.dialog.open(ShareDialogComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				title: this.translate.instant('hawb.share.title'),
				shareType: ShareType.HAWB,
				param: event.hawbId,
			},
		});
	}

	onHawbListPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getHawbListPage(this.pageParams);
	}

	private getHawbListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.hawbList = [];

		this.hawbSearchRequestService
			.getHawbList(pageParams, this.hawbSearchPayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.hawbList = res.rows;
					this.hawbListTotalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
