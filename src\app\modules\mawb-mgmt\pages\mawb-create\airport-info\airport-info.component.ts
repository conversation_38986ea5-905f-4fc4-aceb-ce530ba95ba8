import { ChangeDetectionStrategy, Component, DestroyRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { TranslateModule } from '@ngx-translate/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../../../sli-mgmt/services/sli-create-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { MatIconModule } from '@angular/material/icon';
import { provideNativeDateAdapter } from '@angular/material/core';

const REGX_NUMBER_2_DECIMAL = '^\\d+(\\.\\d{1,2})?$';

@Component({
	selector: 'orll-airport-info',
	imports: [
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		MatIconModule,
		TranslateModule,
		CurrencyInputComponent,
		MatSelectModule,
		MatAutocompleteModule,
		MatDatepickerModule,
	],
	templateUrl: './airport-info.component.html',
	styleUrl: './airport-info.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [provideNativeDateAdapter()],
})
export class AirportInfoComponent implements OnInit {
	@Input()
	currencies: string[] = [];

	@Input()
	flightDisabled = false;

	@Output()
	wtOrValChange = new EventEmitter<MatSelectChange>();

	airportInfoForm = this.fb.group({
		departureAndRequestedRouting: ['', [Validators.required]],
		airportOfDestination: ['', [Validators.required]],
		amountOfInsurance: new FormGroup({
			id: new FormControl<string | null>(null),
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<string>('NIL', [Validators.pattern('^NIL$|' + REGX_NUMBER_2_DECIMAL)]),
		}),
		chargesCode: [''],
		// below is right side form controls
		flight: new FormControl<string>({ value: '', disabled: true }),
		to: new FormControl<string>({ value: '', disabled: true }),
		toBy2ndCarrier: new FormControl<string>({ value: '', disabled: true }),
		toBy3rdCarrier: new FormControl<string>({ value: '', disabled: true }),
		date: new FormControl<Date | null>({ value: null, disabled: true }),
		byFirstCarrier: new FormControl<string>({ value: '', disabled: true }),
		by2ndCarrier: new FormControl<string>({ value: '', disabled: true }),
		by3rdCarrier: new FormControl<string>({ value: '', disabled: true }),
		wtOrVal: new FormControl<string>({ value: '', disabled: true }, [Validators.required]),
		other: new FormControl<string>({ value: '', disabled: true }, [Validators.required]),
		declaredValueForCarriage: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<string>('NCV', [Validators.pattern('^NCV$|' + REGX_NUMBER_2_DECIMAL)]),
		}),
		declaredValueForCustoms: new FormGroup({
			currencyUnit: new FormControl(''),
			numericalValue: new FormControl<string>('NVD', [Validators.pattern('^NVD$|' + REGX_NUMBER_2_DECIMAL)]),
		}),
	});

	airports: CodeName[] = [];
	filteredDepartureAirports: CodeName[] = [];
	filteredArrivalAirports: CodeName[] = [];
	allChargesCode: CodeName[] = [];
	filteredChargesCode: CodeName[] = [];
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	constructor(
		private readonly destroyRef: DestroyRef,
		private readonly fb: NonNullableFormBuilder,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly mgmtRequestService: OrgMgmtRequestService
	) {}

	ngOnInit(): void {
		if (!this.flightDisabled) {
			this.airportInfoForm.get('flight')?.enable();
			this.airportInfoForm.get('to')?.enable();
			this.airportInfoForm.get('date')?.enable();
			this.airportInfoForm.get('byFirstCarrier')?.enable();
			this.airportInfoForm.get('flight')?.setValidators(Validators.required);
			this.airportInfoForm.get('date')?.setValidators(Validators.required);
		} else {
			this.airportInfoForm.get('wtOrVal')?.enable();
			this.airportInfoForm.get('other')?.enable();
		}
		this.initRefData();
		this.setupAutocomplete();
	}

	displayAirportName = (code: string): string => {
		const airport = this.airports.find((item) => item.code === code);
		return airport?.name ?? '';
	};

	displayChargesCodeName = (code: string): string => {
		const chargesCode = this.allChargesCode.find((item) => item.code === code);
		return chargesCode?.name ?? '';
	};

	private initRefData(): void {
		this.sliCreateRequestService.getAirports().subscribe((airports: CodeName[]) => {
			this.airports = airports;
			this.filteredDepartureAirports = [...airports];
		});
		this.enumRequest(this.enumCodeTypeModel.CHARGE_CODE);
	}

	enumRequest(enumType: EnumCodeTypeModel): void {
		this.mgmtRequestService.getEnumCode(enumType).subscribe((res: CodeName[]) => {
			this.allChargesCode = res;
			this.filteredChargesCode = res;
		});
	}

	private setupAutocomplete(): void {
		this.airportInfoForm
			.get('departureAndRequestedRouting')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDepartureAirports = this.filterAirports(search);
			});

		this.airportInfoForm
			.get('airportOfDestination')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredArrivalAirports = this.filterAirports(search);
			});

		this.airportInfoForm
			.get('chargesCode')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredChargesCode = this.allChargesCode.filter((item) =>
					item.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	filterAirports(search: string): CodeName[] {
		return this.airports.filter((airport) => airport.code.toLowerCase().includes(search.toLowerCase().trim()));
	}

	onToByChange(event: Event, type: string): void {
		const value = (event.target as HTMLInputElement).value;
		switch (type) {
			case 't1':
				if (value) {
					this.airportInfoForm.get('toBy2ndCarrier')?.enable();
				} else {
					this.airportInfoForm.get('toBy2ndCarrier')?.patchValue('');
					this.airportInfoForm.get('toBy2ndCarrier')?.disable();
					this.airportInfoForm.get('toBy3rdCarrier')?.patchValue('');
					this.airportInfoForm.get('toBy3rdCarrier')?.disable();
				}
				break;
			case 't2':
				if (value) {
					this.airportInfoForm.get('toBy3rdCarrier')?.enable();
				} else {
					this.airportInfoForm.get('toBy3rdCarrier')?.patchValue('');
					this.airportInfoForm.get('toBy3rdCarrier')?.disable();
				}
				break;
			case 'b1':
				if (value) {
					this.airportInfoForm.get('by2ndCarrier')?.enable();
				} else {
					this.airportInfoForm.get('by2ndCarrier')?.patchValue('');
					this.airportInfoForm.get('by2ndCarrier')?.disable();
					this.airportInfoForm.get('by3rdCarrier')?.patchValue('');
					this.airportInfoForm.get('by3rdCarrier')?.disable();
				}
				break;
			case 'b2':
				if (value) {
					this.airportInfoForm.get('by3rdCarrier')?.enable();
				} else {
					this.airportInfoForm.get('by3rdCarrier')?.patchValue('');
					this.airportInfoForm.get('by3rdCarrier')?.disable();
				}
				break;

			default:
				break;
		}
	}
}
