import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, Routes } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { UserRole } from '@shared/models/user-role.model';
import { CanDeactivateGuard } from '@shared/services/can-deactivate.guard';
import { RouteGuardService } from '@shared/services/route-guard.service';

const MENU_SLI_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER];
const MENU_HAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];
const MENU_MAWB_ROLES: string[] = [UserRole.FORWARDER, UserRole.CARRIER];
const PARTNER_MANAGE_ROLES: string[] = [UserRole.SHIPPER, UserRole.FORWARDER, UserRole.CARRIER];

export const ROUTES: Routes = [
	{
		path: 'sli',
		data: {
			breadcrumb: {
				label: 'sli.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_SLI_ROLES)],
			},
			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.create',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard],
					},
					{
						path: 'piece/:pieceType/:sliNumber',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard],
						data: {
							breadcrumb: {
								label: 'sli.piece.add',
							},
						},
					},
				],
			},
			{
				path: 'edit/:sliNumber/:tabIndex',
				data: {
					breadcrumb: {
						label: 'sli.mgmt.edit',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-create/sli-create-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard],
					},
					{
						path: 'piece/:pieceType/:pieceId',
						loadComponent: () => import('./modules/sli-mgmt/pages/piece-add/piece-add-page.component'),
						canDeactivate: [CanDeactivateGuard],
						canActivate: [MsalGuard],
						data: {
							breadcrumb: {
								label: 'sli.piece.edit',
							},
						},
					},
				],
			},
		],
	},

	{
		path: 'hawb',
		data: {
			breadcrumb: {
				label: 'hawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_HAWB_ROLES)],
			},
			{
				path: 'edit/:hawbId',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.edit',
					},
				},
				loadComponent: () => import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
				canActivate: [MsalGuard],
			},

			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'hawb.mgmt.create.fromSli',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/sli-mgmt/pages/sli-list/sli-list-page.component'),
						canActivate: [MsalGuard],
						data: {
							fromCreateHawb: true,
						},
					},
					{
						path: ':fromSli/detail',
						data: {
							breadcrumb: {
								label: 'hawb.mgmt.create.fromSliDetail',
							},
						},
						loadComponent: () =>
							import('./modules/hawb-mgmt/pages/create-hawb-from-shared-sli/create-hawb-from-shared-sli.component'),
						resolve: {
							sliNumber: (route: ActivatedRouteSnapshot) => route.paramMap.get('fromSli'),
						},
						canActivate: [MsalGuard],
					},
				],
			},

			{
				path: 'status-tracking',
				loadComponent: () =>
					import('./modules/hawb-mgmt/pages/status-tracking/status-tracking.component').then((it) => it.StatusTrackingComponent),
			},
		],
	},

	{
		path: 'mawb',
		data: {
			breadcrumb: {
				label: 'mawb.mgmt.title',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-list/mawb-list.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(MENU_MAWB_ROLES)],
			},

			{
				path: 'edit/:mawbId',
				data: {
					breadcrumb: {
						label: 'mawb.mgmt.edit',
					},
				},
				loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-create/create-mawb-from-hawb.component'),
				canActivate: [MsalGuard],
			},

			{
				path: 'create',
				data: {
					breadcrumb: {
						label: 'mawb.mgmt.create.fromHawb',
					},
				},
				children: [
					{
						path: '',
						loadComponent: () => import('./modules/hawb-mgmt/pages/hawb-list/hawb-list-page.component'),
						canActivate: [MsalGuard],
						data: {
							fromCreateMawb: true,
						},
					},
					{
						path: 'detail',
						data: {
							breadcrumb: {
								label: 'mawb.mgmt.create.fromHawbDetail',
							},
						},
						loadComponent: () => import('./modules/mawb-mgmt/pages/mawb-create/create-mawb-from-hawb.component'),
						canActivate: [MsalGuard],
					},
				],
			},
		],
	},
	{
		path: 'partner',
		data: {
			breadcrumb: {
				label: 'partner.mgmt.head',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/partner-access/components/partner-access/partner-access.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).hasSomeRole(PARTNER_MANAGE_ROLES)],
			},
		],
	},

	{
		path: 'users-mgmt',
		data: {
			breadcrumb: {
				label: 'users.mgmt.list',
			},
		},
		children: [
			{
				path: '',
				loadComponent: () => import('./modules/user-mgmt/pages/user-list/user-list-page.component'),
				canActivate: [MsalGuard, () => inject(RouteGuardService).isSuperUser()],
			},
		],
	},

	// Role-based default redirect
	{
		path: '',
		loadComponent: () => import('./shared/components/redirect/redirect.component').then((m) => m.default),
		canActivate: [MsalGuard],
		pathMatch: 'full',
	},
	{ path: '**', redirectTo: '' },
] as Routes;
