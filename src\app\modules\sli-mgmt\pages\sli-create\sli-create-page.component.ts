import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router } from '@angular/router';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ShipmentParty } from '../../models/shipment-party.model';
import { SliShipperComponent } from '../../components/sli-shipper/sli-shipper.component';
import { SliConsigneeComponent } from '../../components/sli-consignee/sli-consignee.component';
import { SliRoutingComponent } from '../../components/sli-routing/sli-routing.component';
import { SliPieceListComponent } from '../../components/sli-piece-list/sli-piece-list.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SliCreatePayload } from '../../models/sli-create-payload.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatDialog } from '@angular/material/dialog';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { ConfirmDialogComponent } from '@shared/components/confirm-dialog/confirm-dialog.component';
import { map, Observable, startWith } from 'rxjs';
import { CanComponentDeactivate } from '@shared/services/can-deactivate.guard';
import { Person } from '@shared/models/person.model';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { AsyncPipe, CommonModule } from '@angular/common';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { Modules, UserPermission } from '@shared/models/user-role.model';
import { MatTabChangeEvent, MatTabGroup, MatTabsModule } from '@angular/material/tabs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { DropDownType } from '@shared/models/dropdown-type.model';

const REGX_NUMBER_1_DECIMAL = '^\\d+(\\.\\d{1})?$';
const REGX_NUMBER_2_DECIMAL = /^\d+(\.\d{1,2})?$/;
const SLI_TAB_INDEX = 0;
const PIECE_TAB_INDEX = 1;

@Component({
	selector: 'orll-sli-create-page',
	templateUrl: './sli-create-page.component.html',
	styleUrls: ['./sli-create-page.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatIconModule,
		MatButtonModule,
		MatTabsModule,
		MatExpansionModule,
		TranslateModule,
		SliShipperComponent,
		SliConsigneeComponent,
		SliRoutingComponent,
		SliPieceListComponent,
		SpinnerComponent,
		AsyncPipe,
		MatInputModule,
		MatSelectModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
	],
})
export default class SliCreatePageComponent extends RolesAwareComponent implements OnInit, CanComponentDeactivate {
	@ViewChildren(SliShipperComponent) sliShipper!: QueryList<SliShipperComponent>;
	@ViewChildren('sliConsignee') sliConsignee!: QueryList<SliConsigneeComponent>;
	@ViewChildren(SliRoutingComponent) sliRouting!: QueryList<SliRoutingComponent>;
	@ViewChildren(SliPieceListComponent) sliPieceList!: QueryList<SliPieceListComponent>;
	@ViewChild('tabGroup') tabGroup!: MatTabGroup;
	@Input() sliNumber = '';

	selectedTabIndex = 0;
	shipperInfo: OrgInfo | ShipmentParty | null = null;
	consigneeInfo: ShipmentParty | null = null;
	alsoNotifies: ShipmentParty[] = [];
	dataLoading = false;
	isSaved = false;
	isConfirmed = false;
	isPiece = false;
	pieceType = '';
	sliForm: FormGroup = new FormGroup({
		goodsDescription: new FormControl<string>('', [Validators.required]),
		totalGrossWeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimLength: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimWidth: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		dimHeight: new FormControl<string>('', [Validators.required, Validators.pattern(REGX_NUMBER_1_DECIMAL)]),
		declaredValueForCustoms: new FormControl<string>('NCV', [Validators.pattern('^NCV$|^\\d+(\\.\\d{1,2})?$')]),
		declaredValueForCarriage: new FormControl<string>('NVD', [Validators.pattern('^NVD$|^\\d+(\\.\\d{1,2})?$')]),
		insuredAmount: new FormControl<string>('NIL', [Validators.pattern('^NIL$|^\\d+(\\.\\d{1,2})?$')]),
		declaredValueForCustomsCurrency: new FormControl<string>(''),
		declaredValueForCarriageCurrency: new FormControl<string>(''),
		insuredAmountCurrency: new FormControl<string>(''),
		textualHandlingInstructions: new FormControl<string>(''),
		weightValuationIndicator: new FormControl<string>(''),
		incoterms: new FormControl<string>(''),
	});
	weightValuationIndicators: string[] = [DropDownType.PREPAID, DropDownType.COLLECT];
	incoterms: CodeName[] = [];
	filteredDeclaredCustomsCurrency: string[] = [];
	filteredDeclaredCarriageCurrency: string[] = [];
	filteredInsuredAmountCurrency: string[] = [];
	currencies: string[] = [];
	routeTabIndex: number | null = null;

	readonly sliModule = Modules.SLI;
	readonly savePermission = [UserPermission.CREATE, UserPermission.UPDATE].join(',');

	constructor(
		private readonly router: Router,
		private readonly route: ActivatedRoute,
		private readonly cdr: ChangeDetectorRef,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly translate: TranslateService,
		private readonly dialog: MatDialog
	) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
		if (!this.sliNumber) {
			this.fillShipperInfo();
		} else {
			this.getSliDetail(this.sliNumber);
		}
		this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params) => {
			const tabIndex = +params['tabIndex'];
			if (!isNaN(tabIndex)) {
				this.selectedTabIndex = tabIndex;
				this.routeTabIndex = tabIndex;
			}
		});
	}

	getSliDetail(sliNumber: string): void {
		this.sliCreateRequestService
			.getSliDetail(sliNumber)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					const data = res;
					this.shipperInfo = data.shipmentParty.find((item) => item.companyType === OrgType.SHIPPER) ?? null;
					this.consigneeInfo = data.shipmentParty.find((item) => item.companyType === OrgType.CONSIGNEE) ?? null;
					this.alsoNotifies = data.shipmentParty.filter((item) => !item.companyType) ?? [];
					this.sliRouting.first?.sliRoutingForm.patchValue({
						departureLocation: data.departureLocation,
						arrivalLocation: data.arrivalLocation,
						shippingInfo: data.shippingInfo,
					});
					this.sliForm.patchValue({
						goodsDescription: data.goodsDescription,
						totalGrossWeight: data.totalGrossWeight,
						dimLength: data.totalDimensions.length,
						dimWidth: data.totalDimensions.width,
						dimHeight: data.totalDimensions.height,
						declaredValueForCustoms: data.declaredValueForCustoms?.numericalValue ?? 'NCV',
						declaredValueForCarriage: data.declaredValueForCarriage?.numericalValue ?? 'NVD',
						insuredAmount: data.insuredAmount?.numericalValue ?? 'NIL',
						declaredValueForCustomsCurrency: data.declaredValueForCustoms?.currencyUnit ?? '',
						declaredValueForCarriageCurrency: data.declaredValueForCarriage?.currencyUnit ?? '',
						insuredAmountCurrency: data.insuredAmount?.currencyUnit ?? '',
						textualHandlingInstructions: data.textualHandlingInstructions,
						weightValuationIndicator: data.weightValuationIndicator,
						incoterms: data.incoterms,
					});
					this.cdr.markForCheck();
				},
			});
	}

	fillShipperInfo(): void {
		this.getCurrentUser().subscribe((user) => {
			if (user?.primaryOrgId) {
				this.orgMgmtRequestService
					.getOrgInfo(user.primaryOrgId)
					.pipe(takeUntilDestroyed(this.destroyRef))
					.subscribe({
						next: (res) => {
							this.shipperInfo = res;
							this.cdr.markForCheck();
						},
					});
			}
		});
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			textualPostCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					return {
						...item,
						companyName: result.companyName,
						contactName:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						textualPostCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
						emailAddress:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
						companyType: '',
					};
				}
				return item;
			});

			this.cdr.markForCheck();
		});
	}

	private sliDetailRequest(sliCreatePayload: SliCreatePayload): Observable<string | null> {
		if (!this.sliNumber) {
			return this.sliCreateRequestService.createSli(sliCreatePayload);
		} else {
			return this.sliCreateRequestService.updateSli(sliCreatePayload, this.sliNumber);
		}
	}

	onSave(pieceParams?: { pieceType: string; pieceId?: string }): void {
		this.sliShipper.forEach((comp) => comp.sliShipperForm?.markAllAsTouched());
		this.sliRouting.forEach((comp) => comp.sliRoutingForm?.markAllAsTouched());
		this.sliForm?.markAllAsTouched();

		const shipperData = this.sliShipper.first?.getFormData();
		const consigneeData = this.sliConsignee.first?.getFormData();
		const routingData = this.sliRouting.first?.getFormData();
		const pieceListData = this.getFormData();
		if (pieceParams) {
			if (!this.sliNumber) {
				this.dialog.open(ConfirmDialogComponent, {
					width: '300px',
					data: {
						content: this.translate.instant('sli.piece.no.sli.fail'),
					},
				});

				return;
			}
		}

		if (!shipperData || !consigneeData || !routingData || !pieceListData) {
			this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.form.validate'),
				},
			});

			return;
		}

		shipperData.companyType = OrgType.SHIPPER;
		const sliCreatePayload = {
			shipmentParty: [shipperData, consigneeData, ...this.alsoNotifies],
			...routingData,
			...pieceListData,
		} as SliCreatePayload;

		this.dataLoading = true;

		this.sliDetailRequest(sliCreatePayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dataLoading = false;
					this.isSaved = true;
					this.cdr.markForCheck();

					const sliNumber = res;
					let routePath = [];
					if (pieceParams) {
						if (pieceParams.pieceId) {
							routePath = ['piece', pieceParams.pieceType, pieceParams.pieceId];
						} else {
							routePath = ['piece', pieceParams.pieceType, this.sliNumber ?? sliNumber];
						}
					} else {
						routePath = ['sli'];
					}
					this.router.navigate(routePath, { relativeTo: this.route });
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	onCancel(): void {
		if (this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			dialogRef.afterClosed().subscribe((confirmed) => {
				if (confirmed) {
					this.isConfirmed = true;
					this.router.navigate(['/sli'], { relativeTo: this.route });
				}
			});
		} else {
			this.router.navigate(['/sli'], { relativeTo: this.route });
		}
	}

	canDeactivate(): boolean | Observable<boolean> {
		if (this.isSaved) return true;

		if (!this.isConfirmed && this.hasUnsavedChanges()) {
			const dialogRef = this.dialog.open(ConfirmDialogComponent, {
				width: '300px',
				data: {
					content: this.translate.instant('common.dialog.cancel.content'),
				},
			});

			return dialogRef.afterClosed().pipe(map((confirmed) => !!confirmed));
		} else {
			return true;
		}
	}

	hasUnsavedChanges(): boolean {
		const shipperData = this.sliShipper.first?.getFormData(true);
		const hasShipperData = shipperData ? this.hasRealData(shipperData) : false;

		const consigneeData = this.sliConsignee.first?.getFormData();
		const hasConsigneeData = consigneeData ? this.hasRealData(consigneeData) : false;

		const routingData = this.sliRouting.first?.getFormData(true);
		const hasRoutingData = routingData ? this.hasRealData(routingData) : false;

		const pieceListData = this.sliPieceList.first?.getFormData(true);
		const hasPieceListData = pieceListData ? this.hasRealData(pieceListData) : false;

		const pieceListTableData = this.sliPieceList.first?.pieceList;
		const haspieceListTableData = pieceListData ? this.hasRealData(pieceListTableData) : false;

		const hasAlsoNotifies = this.alsoNotifies.some((item) => this.hasRealData(item));

		return hasShipperData || hasConsigneeData || hasAlsoNotifies || hasRoutingData || hasPieceListData || haspieceListTableData;
	}

	hasRealData(obj: Record<string, any>): boolean {
		if (!obj) return false;
		return Object.values(obj).some((value) => {
			if (Array.isArray(value)) return value.length > 0;
			if (typeof value === 'object') return this.hasRealData(value);
			return String(value).trim();
		});
	}

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		if (!ignore && this.sliForm.invalid) {
			return null;
		}
		return {
			goodsDescription: this.sliForm.value.goodsDescription!,
			totalGrossWeight: this.sliForm.value.totalGrossWeight ? Number(this.sliForm.value.totalGrossWeight) : '',
			totalDimensions: {
				length: this.sliForm.value.dimLength ? Number(this.sliForm.value.dimLength) : '',
				width: this.sliForm.value.dimWidth ? Number(this.sliForm.value.dimWidth) : '',
				height: this.sliForm.value.dimHeight ? Number(this.sliForm.value.dimHeight) : '',
			},
			insuredAmount: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliForm.value.insuredAmount)
					? Number(this.sliForm.value.insuredAmount)
					: null,
				currencyUnit: this.sliForm.value.insuredAmountCurrency,
			},
			declaredValueForCarriage: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliForm.value.declaredValueForCarriage)
					? Number(this.sliForm.value.declaredValueForCarriage)
					: null,
				currencyUnit: this.sliForm.value.declaredValueForCarriageCurrency,
			},
			declaredValueForCustoms: {
				numericalValue: REGX_NUMBER_2_DECIMAL.test(this.sliForm.value.declaredValueForCustoms)
					? Number(this.sliForm.value.declaredValueForCustoms)
					: null,
				currencyUnit: this.sliForm.value.declaredValueForCustomsCurrency,
			},
			weightValuationIndicator: this.sliForm.value.weightValuationIndicator,
			textualHandlingInstructions: this.sliForm.value.textualHandlingInstructions,
			incoterms: this.sliForm.value.incoterms,
			pieces: [], // update SLI only without piece list data
		};
	}

	private setupAutocomplete(): void {
		this.sliForm
			.get('declaredValueForCustomsCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDeclaredCustomsCurrency = this.filterCurrency(search);
			});

		this.sliForm
			.get('declaredValueForCarriageCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredDeclaredCarriageCurrency = this.filterCurrency(search);
			});

		this.sliForm
			.get('insuredAmountCurrency')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredInsuredAmountCurrency = this.filterCurrency(search);
			});
	}

	filterCurrency(search: string): string[] {
		return this.currencies.filter((currency) => currency.toLowerCase().includes(search?.toLowerCase().trim() ?? ''));
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCurrencies().subscribe((currencies: string[]) => {
			this.currencies = ['', ...currencies];
			this.filteredInsuredAmountCurrency = ['', ...currencies];
		});

		this.sliCreateRequestService.getIncoterms().subscribe((incoterms: CodeName[]) => {
			this.incoterms = incoterms;
		});
	}

	onTabChanged($event: MatTabChangeEvent) {
		this.selectedTabIndex = $event.index;
		if (this.sliNumber && this.selectedTabIndex === SLI_TAB_INDEX && this.routeTabIndex === PIECE_TAB_INDEX) {
			setTimeout(() => {
				this.getSliDetail(this.sliNumber);
			}, 2000);
		}
	}
}
