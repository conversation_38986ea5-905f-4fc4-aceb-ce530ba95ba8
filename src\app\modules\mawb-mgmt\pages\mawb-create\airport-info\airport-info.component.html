<div>
	<form [formGroup]="airportInfoForm">
		<div class="row">
			<div class="col width-500">
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.departureAndRequestedRouting' | translate }}</mat-label>
						<input type="text" matInput formControlName="departureAndRequestedRouting" [matAutocomplete]="autoDeparture" />
						<mat-autocomplete #autoDeparture="matAutocomplete" [displayWith]="displayAirportName">
							@for (airport of filteredDepartureAirports; track airport) {
								<mat-option [value]="airport.code">{{ airport.name }}</mat-option>
							}
						</mat-autocomplete>
						@if (airportInfoForm.get('departureAndRequestedRouting')?.hasError('required')) {
							<mat-error>{{
								'validators.required'
									| translate: { field: 'hawb.airportInfo.departureAndRequestedRouting.required' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.airportOfDestination' | translate }}</mat-label>
						<input type="text" matInput formControlName="airportOfDestination" [matAutocomplete]="autoArrival" />
						<mat-autocomplete #autoArrival="matAutocomplete" [displayWith]="displayAirportName">
							@for (airport of filteredArrivalAirports; track airport) {
								<mat-option [value]="airport.code">{{ airport.name }}</mat-option>
							}
						</mat-autocomplete>
						@if (airportInfoForm.get('airportOfDestination')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.airportOfDestination' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<orll-currency-input
						class="col-6"
						formLabel="hawb.airportInfo.amountOfInsurance"
						[hiddenUnit]="true"
						[currencies]="currencies"
						[currencyForm]="airportInfoForm.controls.amountOfInsurance"></orll-currency-input>
					@if (!flightDisabled) {
						<mat-form-field appearance="outline" class="col-6" floatLabel="always">
							<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
							<mat-label>{{ 'mawb.airportInfo.chargesCode' | translate }}</mat-label>
							<input type="text" matInput formControlName="chargesCode" [matAutocomplete]="autoChargesCode" />
							<mat-autocomplete #autoChargesCode="matAutocomplete" [displayWith]="displayChargesCodeName">
								@for (chargesCode of filteredChargesCode; track chargesCode) {
									<mat-option [value]="chargesCode.code">{{ chargesCode.name }}</mat-option>
								}
							</mat-autocomplete>
						</mat-form-field>
					}
				</div>
			</div>
			<div class="col">
				<div class="row">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.flight' | translate }}</mat-label>
						<input matInput formControlName="flight" />
						@if (airportInfoForm.get('flight')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.airportInfo.flight' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.to' | translate }}</mat-label>
						<input matInput formControlName="to" (input)="onToByChange($event, 't1')" />
						@if (airportInfoForm.get('to')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.airportInfo.to' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.toBy2ndCarrier' | translate }}</mat-label>
						<input matInput formControlName="toBy2ndCarrier" (input)="onToByChange($event, 't2')" />
						@if (airportInfoForm.get('toBy2ndCarrier')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.toBy2ndCarrier' | translate }
							}}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.toBy3rdCarrier' | translate }}</mat-label>
						<input matInput formControlName="toBy3rdCarrier" />
						@if (airportInfoForm.get('toBy3rdCarrier')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.toBy3rdCarrier' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.date' | translate }}</mat-label>
						<input matInput formControlName="date" [matDatepicker]="picker" />
						<mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
						<mat-datepicker #picker></mat-datepicker>
						@if (airportInfoForm.get('date')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.airportInfo.date' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.byFirstCarrier' | translate }}</mat-label>
						<input matInput formControlName="byFirstCarrier" (input)="onToByChange($event, 'b1')" />
						@if (airportInfoForm.get('byFirstCarrier')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.byFirstCarrier' | translate }
							}}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.by2ndCarrier' | translate }}</mat-label>
						<input matInput formControlName="by2ndCarrier" (input)="onToByChange($event, 'b2')" />
						@if (airportInfoForm.get('by2ndCarrier')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.by2ndCarrier' | translate }
							}}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.by3rdCarrier' | translate }}</mat-label>
						<input matInput formControlName="by3rdCarrier" />
						@if (airportInfoForm.get('by3rdCarrier')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.by3rdCarrier' | translate }
							}}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.wtOrVal' | translate }}</mat-label>
						<mat-select formControlName="wtOrVal" (selectionChange)="wtOrValChange.emit($event)">
							<mat-option value="Prepaid">Prepaid</mat-option>
							<mat-option value="Collect">Collect</mat-option>
						</mat-select>

						@if (airportInfoForm.get('wtOrVal')?.hasError('required')) {
							<mat-error>{{
								'validators.required' | translate: { field: 'hawb.airportInfo.wtOrVal' | translate }
							}}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.airportInfo.other' | translate }}</mat-label>
						<mat-select formControlName="other">
							<mat-option value="Prepaid">Prepaid</mat-option>
							<mat-option value="Collect">Collect</mat-option>
						</mat-select>
						@if (airportInfoForm.get('other')?.hasError('required')) {
							<mat-error>{{ 'validators.required' | translate: { field: 'hawb.airportInfo.other' | translate } }}</mat-error>
						}
					</mat-form-field>

					<orll-currency-input
						class="col-3"
						formLabel="hawb.airportInfo.declaredValueForCarriage"
						[hiddenUnit]="true"
						[currencies]="currencies"
						[currencyForm]="airportInfoForm.controls.declaredValueForCarriage"></orll-currency-input>

					<orll-currency-input
						class="col-3"
						formLabel="hawb.airportInfo.declaredValueForCustoms"
						[hiddenUnit]="true"
						[currencies]="currencies"
						[currencyForm]="airportInfoForm.controls.declaredValueForCustoms"></orll-currency-input>
				</div>
			</div>
		</div>
	</form>
</div>
