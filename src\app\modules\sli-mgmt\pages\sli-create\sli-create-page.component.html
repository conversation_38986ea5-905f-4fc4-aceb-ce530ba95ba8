<div class="orll-sli-create-page iata-box">
	<mat-tab-group #tabGroup mat-stretch-tabs="false" mat-align-tabs="start" class="orll-sli-create-page__tab_group"
		dynamicHeight [selectedIndex]="selectedTabIndex" (selectedTabChange)="onTabChanged($event)">
		<mat-tab [label]="'common.mainHeader.mainNav.sli' | translate" class="orll-sli-create-page__tab">
			<div class="tab-content-container">
				<div class="row">
					<div class="iata-shipper-box orll-sli-shipper__box ">
						<orll-sli-shipper #sliShipper [shipperInfo]="shipperInfo"></orll-sli-shipper>
					</div>
					<div class="iata-shipper-box orll-sli-consignee__box">
						<orll-sli-consignee #sliConsignee title="consignee"
							[shipmentParty]="consigneeInfo"></orll-sli-consignee>
					</div>
				</div>

				<div class="row">
					<div class="iata-shipper-box orll-sli-also-notify__box col-12">
						@for (alsoNotify of alsoNotifies; track $index) {
							<mat-expansion-panel class="orll-sli-also-notify__panel" [expanded]="true">
								<mat-expansion-panel-header>
									<mat-panel-title>
										<h2 class="mat-display-2 orll-sli-also-notify__title">
											{{'sli.mgmt.company.alsoNotify' | translate}}
										</h2>
									</mat-panel-title>
									<mat-panel-description>
										<button mat-icon-button color="primary" (click)="delAlsoNotify($index, $event)"
											class="orll-sli-also-notify__delete-button">
											<mat-icon>delete</mat-icon>
										</button>
										<button mat-icon-button color="primary" (click)="getOrgList($index, $event)"
											class="orll-sli-also-notify__contact-button">
											<mat-icon>contacts</mat-icon>
										</button>
									</mat-panel-description>
								</mat-expansion-panel-header>
								<orll-sli-consignee #sliAlsoNotify [shipmentParty]="alsoNotify"></orll-sli-consignee>
							</mat-expansion-panel>
						}

						<div class="orll-sli-also-notify__footer">
							<button mat-stroked-button color="primary" type="button" (click)="addAlsoNotify()"
								class="orll-sli-also-notify__add-button">
								<mat-icon>add</mat-icon>
								{{'sli.mgmt.company.alsoNotify' | translate}}
							</button>
						</div>
					</div>
				</div>


				<div class="row margin-r-5">
					<div class="iata-shipper-box orll-sli-routing__box col-12">
						<orll-sli-routing #sliRouting></orll-sli-routing>
					</div>
				</div>
				<div class="row">
					<div class="iata-shipper-box orll-sli-shipper__box">
						<form [formGroup]="sliForm">
							<div class="row">
								<mat-form-field appearance="outline" class="width-100" floatLabel="always">
									<mat-label>{{'sli.mgmt.pieceList.goodsDescription' | translate}}</mat-label>
									<textarea matInput formControlName="goodsDescription" rows="4" required></textarea>
									@if (sliForm.get('goodsDescription')?.hasError('required')) {
										<mat-error>{{'sli.mgmt.pieceList.goodsDescription.required' | translate}}</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="row">
								<div class="input-container">
									<mat-form-field appearance="outline" floatLabel="always" class="input-box">
										<mat-label>{{'sli.mgmt.pieceList.declaredValueForCustoms' |
											translate}}</mat-label>
										<div matInput class="unit-row">
											<input formControlName="declaredValueForCustoms"
												(click)="$event.stopPropagation();">
											<span matSuffix class="vertical-divider"></span>
											<input type="text" matInput
												formControlName="declaredValueForCustomsCurrency"
												[matAutocomplete]="autoCustomsCurrency" class="unit-box">
											<mat-autocomplete #autoCustomsCurrency="matAutocomplete">
												@for (currency of filteredDeclaredCustomsCurrency; track currency) {
													<mat-option [value]="currency">{{currency}}</mat-option>
												}
											</mat-autocomplete>
											<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
										</div>
										@if (sliForm.get('declaredValueForCustoms')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NCV' | translate}}</mat-error>
										}
									</mat-form-field>

								</div>

								<div class="input-container">
									<mat-form-field appearance="outline" floatLabel="always" class="input-box">
										<mat-label>{{'sli.mgmt.pieceList.declaredValueForCarriage' | translate}}</mat-label>
										<div matInput class="unit-row">
											<input formControlName="declaredValueForCarriage"
												(click)="$event.stopPropagation();">
											<span matSuffix class="vertical-divider"></span>
											<input type="text" matInput formControlName="declaredValueForCarriageCurrency"
												[matAutocomplete]="autoCarriageCurrency" class="unit-box">

											<mat-autocomplete #autoCarriageCurrency="matAutocomplete">
												@for (currency of filteredDeclaredCarriageCurrency; track currency) {
													<mat-option [value]="currency">{{currency}}</mat-option>
												}
											</mat-autocomplete>
											<mat-icon matSuffix
												class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
										</div>
										@if (sliForm.get('declaredValueForCarriage')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NVD' | translate}}</mat-error>
										}
									</mat-form-field>
								</div>

								<div class="input-container">
									<mat-form-field appearance="outline" floatLabel="always" class="input-box">
										<mat-label>{{'sli.mgmt.pieceList.insuredAmount' | translate}}</mat-label>
										<div matInput class="unit-row">
											<input formControlName="insuredAmount" (click)="$event.stopPropagation();">
											<span matSuffix class="vertical-divider"></span>
											<input type="text" matInput formControlName="insuredAmountCurrency"
												[matAutocomplete]="autoInsuredAmountCurrency" class="unit-box">
											<mat-autocomplete #autoInsuredAmountCurrency="matAutocomplete">
												@for (currency of filteredInsuredAmountCurrency; track currency) {
													<mat-option [value]="currency">{{currency}}</mat-option>
												}
											</mat-autocomplete>
											<mat-icon matSuffix
												class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
										</div>
										@if (sliForm.get('insuredAmount')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber2NIL' | translate}}</mat-error>
										}
									</mat-form-field>
								</div>
								<div class="input-container">
									<mat-form-field appearance="outline" floatLabel="always" class="input-box">
										<mat-label>{{'sli.mgmt.pieceList.totalGrossWeight' | translate}}</mat-label>
										<div matInput class="unit-row">
											<input matInput formControlName="totalGrossWeight" required>
											<div matSuffix class="weight-unit">KG</div>
										</div>
										@if (sliForm.get('totalGrossWeight')?.hasError('required')) {
											<mat-error>{{'sli.mgmt.pieceList.totalGrossWeight.required' | translate}}</mat-error>
										}
										@if (sliForm.get('totalGrossWeight')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
										}
									</mat-form-field>
								</div>
								<div class="input-container width-30">
									<mat-form-field appearance="outline" floatLabel="always" class="input-box">
										<mat-label>{{'sli.mgmt.pieceList.totalDimensions' | translate}}</mat-label>
										<div matInput class="all-inputs-row">
											<input matInput formControlName="dimLength"
												placeholder="{{'sli.mgmt.pieceList.dimLength' | translate}}" required
												(click)="$event.stopPropagation();">
											<span matSuffix>CM</span>
											<span class="divider"></span>
											<input matInput formControlName="dimWidth"
												placeholder="{{'sli.mgmt.pieceList.dimWidth' | translate}}" required
												(click)="$event.stopPropagation();">
											<span matSuffix>CM</span>
											<span class="divider"></span>
											<input matInput formControlName="dimHeight"
												placeholder="{{'sli.mgmt.pieceList.dimHeight' | translate}}" required
												(click)="$event.stopPropagation();">
											<span matSuffix>CM</span>
										</div>
										@if (sliForm.get('dimLength')?.hasError('required')) {
											<mat-error>{{'sli.mgmt.pieceList.dimLength.required' | translate}}</mat-error>
										} @else if (sliForm.get('dimLength')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
										} @else if (sliForm.get('dimWidth')?.hasError('required')) {
											<mat-error>{{'sli.mgmt.pieceList.dimWidth.required' | translate}}</mat-error>
										} @else if (sliForm.get('dimWidth')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
										} @else if (sliForm.get('dimHeight')?.hasError('required')) {
											<mat-error>{{'sli.mgmt.pieceList.dimHeight.required' | translate}}</mat-error>
										} @else if (sliForm.get('dimHeight')?.hasError('pattern')) {
											<mat-error>{{'sli.mgmt.pieceList.pattern.decimalNumber1' | translate}}</mat-error>
										}
									</mat-form-field>
								</div>
							</div>
							<div class="row">
								<div class="fill">
									<mat-form-field appearance="outline" class="width-100" floatLabel="always">
										<mat-label>{{'sli.mgmt.pieceList.textualHandlingInstructions' | translate}}</mat-label>
										<textarea matInput formControlName="textualHandlingInstructions"
											rows="4"></textarea>
									</mat-form-field>
								</div>
								<div class="col-2">
									<mat-form-field appearance="outline" class="width-100" floatLabel="always">
										<mat-label>{{'sli.mgmt.pieceList.weightValuationIndicator' | translate}}</mat-label>
										<mat-select formControlName="weightValuationIndicator">
											@for (wvi of weightValuationIndicators; track wvi) {
												<mat-option [value]="wvi">{{wvi}}</mat-option>
											}
										</mat-select>
									</mat-form-field>



									<mat-form-field appearance="outline" class="width-100" floatLabel="always">
										<mat-label>{{'sli.mgmt.pieceList.incoterms' | translate}}</mat-label>
										<mat-select formControlName="incoterms">
											@for (incoterm of incoterms; track incoterm) {
												<mat-option [value]="incoterm.code">{{incoterm.name}}</mat-option>
											}
										</mat-select>
									</mat-form-field>

								</div>
							</div>
						</form>
					</div>
				</div>
			</div>
			<div class="orll-sli-create-page__footer">
				<button mat-stroked-button color="primary" (click)="onCancel()"
					class="orll-sli-create-page__cancel-button">
					{{'sli.mgmt.cancel' | translate}}
				</button>
				@if (hasPermission(savePermission, sliModule) | async) {
					<button mat-flat-button color="primary" (click)="onSave()">
						<mat-icon>save</mat-icon>
						{{'sli.mgmt.save' | translate}}
					</button>
				}
			</div>
		</mat-tab>
		<mat-tab [label]="'common.mainHeader.mainNav.sli.piece' | translate">
			<div class="row margin-r-5">
				<div class="iata-box orll-sli-routing__box col-12">
					<orll-sli-piece-list #sliPieceList [sliNumber]="sliNumber"
						(saveRequest)="onSave($event)"></orll-sli-piece-list>
				</div>
			</div>
		</mat-tab>
	</mat-tab-group>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>
